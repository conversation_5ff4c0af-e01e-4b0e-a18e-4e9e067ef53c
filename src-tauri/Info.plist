<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleName</key>
    <string>Colony</string>
    
    <key>CFBundleDisplayName</key>
    <string>Colony</string>
    
    <key>CFBundleIdentifier</key>
    <string>com.colony.gui</string>
    
    <key>CFBundleVersion</key>
    <string>1.1.2</string>
    
    <key>CFBundleShortVersionString</key>
    <string>1.1.2</string>
    
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    
    <key>CFBundleSignature</key>
    <string>????</string>
    
    <key>CFBundleExecutable</key>
    <string>colony-app</string>
    
    <key>CFBundleIconFile</key>
    <string>icon.icns</string>

    <key>CFBundleIconName</key>
    <string>icon</string>

    <key>LSMinimumSystemVersion</key>
    <string>10.13</string>

    <key>LSApplicationCategoryType</key>
    <string>public.app-category.utilities</string>

    <key>NSHumanReadableCopyright</key>
    <string>Copyright © 2025 Chuck McClish. All rights reserved.</string>

    <key>NSHighResolutionCapable</key>
    <true/>

    <key>NSSupportsAutomaticGraphicsSwitching</key>
    <true/>

    <!-- Required for App Store submission -->
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>

    <!-- App Store specific metadata -->
    <key>ITSAppUsesNonExemptEncryption</key>
    <false/>
    
    <!-- Document types that the app can handle -->
    <key>CFBundleDocumentTypes</key>
    <array>
        <dict>
            <key>CFBundleTypeName</key>
            <string>All Files</string>
            <key>CFBundleTypeRole</key>
            <string>Viewer</string>
            <key>LSItemContentTypes</key>
            <array>
                <string>public.data</string>
            </array>
        </dict>
    </array>
    
    <!-- URL schemes (if needed for future features) -->
    <key>CFBundleURLTypes</key>
    <array>
        <dict>
            <key>CFBundleURLName</key>
            <string>Colony URL Scheme</string>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>colony</string>
            </array>
        </dict>
    </array>
    
    <!-- Privacy usage descriptions -->
    <key>NSNetworkVolumesUsageDescription</key>
    <string>Colony needs network access to connect to the Autonomi network for file storage and retrieval.</string>
    
    <key>NSDownloadsFolderUsageDescription</key>
    <string>Colony needs access to the Downloads folder to save files downloaded from the Autonomi network.</string>
    
    <key>NSDocumentsFolderUsageDescription</key>
    <string>Colony needs access to documents to upload and manage files on the Autonomi network.</string>
</dict>
</plist>
